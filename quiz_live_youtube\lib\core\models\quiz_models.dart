import 'package:freezed_annotation/freezed_annotation.dart';

part 'quiz_models.freezed.dart';
part 'quiz_models.g.dart';

/// Represents a quiz question with multiple choice answers
@freezed
class QuizQuestion with _$QuizQuestion {
  const factory QuizQuestion({
    required String id,
    required String title,
    required String questionText,
    required List<String> options,
    required int correctAnswerIndex,
    @Default(30) int timeLimit,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _QuizQuestion;

  factory QuizQuestion.fromJson(Map<String, dynamic> json) =>
      _$QuizQuestionFromJson(json);
}

/// Represents a complete quiz session
@freezed
class Quiz with _$Quiz {
  const factory Quiz({
    required String id,
    required String title,
    required String description,
    required List<QuizQuestion> questions,
    @Default(false) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Quiz;

  factory Quiz.fromJson(Map<String, dynamic> json) => _$QuizFromJson(json);
}

/// Represents a participant's answer
@freezed
class QuizAnswer with _$QuizAnswer {
  const factory QuizAnswer({
    required String id,
    required String questionId,
    required String participantName,
    required String participantId,
    required int selectedOption,
    required DateTime timestamp,
    @Default(false) bool isCorrect,
  }) = _QuizAnswer;

  factory QuizAnswer.fromJson(Map<String, dynamic> json) =>
      _$QuizAnswerFromJson(json);
}

/// Represents a quiz session result
@freezed
class QuizResult with _$QuizResult {
  const factory QuizResult({
    required String id,
    required String quizId,
    required String questionId,
    required List<QuizAnswer> answers,
    QuizAnswer? winner,
    @Default([]) List<QuizAnswer> topAnswers,
    DateTime? completedAt,
  }) = _QuizResult;

  factory QuizResult.fromJson(Map<String, dynamic> json) =>
      _$QuizResultFromJson(json);
}

/// Represents the current state of a live quiz session
@freezed
class LiveQuizSession with _$LiveQuizSession {
  const factory LiveQuizSession({
    required String id,
    required Quiz quiz,
    @Default(0) int currentQuestionIndex,
    @Default([]) List<QuizResult> results,
    @Default(QuizSessionStatus.waiting) QuizSessionStatus status,
    DateTime? startedAt,
    DateTime? currentQuestionStartedAt,
  }) = _LiveQuizSession;

  factory LiveQuizSession.fromJson(Map<String, dynamic> json) =>
      _$LiveQuizSessionFromJson(json);
}

/// Status of a quiz session
enum QuizSessionStatus {
  waiting,
  active,
  questionActive,
  questionCompleted,
  completed,
  paused,
}

/// Represents leaderboard entry
@freezed
class LeaderboardEntry with _$LeaderboardEntry {
  const factory LeaderboardEntry({
    required String participantName,
    required String participantId,
    @Default(0) int correctAnswers,
    @Default(0) int totalAnswers,
    @Default(0) double averageResponseTime,
    @Default(0) int points,
  }) = _LeaderboardEntry;

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardEntryFromJson(json);
}
