import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/quiz_models.dart';
import 'service_providers.dart';

/// Provider for all quizzes
final quizzesProvider = StateNotifierProvider<QuizzesNotifier, AsyncValue<List<Quiz>>>((ref) {
  final quizService = ref.watch(quizServiceProvider);
  return QuizzesNotifier(quizService);
});

/// Provider for current live quiz session
final liveQuizSessionProvider = StateNotifierProvider<LiveQuizSessionNotifier, LiveQuizSession?>((ref) {
  final quizService = ref.watch(quizServiceProvider);
  return LiveQuizSessionNotifier(quizService);
});

/// Provider for quiz results stream
final quizResultsProvider = StreamProvider<QuizResult>((ref) {
  final quizService = ref.watch(quizServiceProvider);
  return quizService.resultStream;
});

/// Provider for leaderboard
final leaderboardProvider = StateNotifierProvider<LeaderboardNotifier, List<LeaderboardEntry>>((ref) {
  final quizService = ref.watch(quizServiceProvider);
  return LeaderboardNotifier(quizService);
});

/// State notifier for managing quizzes
class QuizzesNotifier extends StateNotifier<AsyncValue<List<Quiz>>> {
  final QuizService _quizService;
  
  QuizzesNotifier(this._quizService) : super(const AsyncValue.loading()) {
    loadQuizzes();
  }
  
  /// Load all quizzes
  Future<void> loadQuizzes() async {
    state = const AsyncValue.loading();
    
    try {
      final quizzes = await _quizService.getAllQuizzes();
      state = AsyncValue.data(quizzes);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Create a new quiz
  Future<void> createQuiz({
    required String title,
    required String description,
    required List<QuizQuestion> questions,
  }) async {
    try {
      await _quizService.createQuiz(
        title: title,
        description: description,
        questions: questions,
      );
      
      // Reload quizzes
      await loadQuizzes();
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Update an existing quiz
  Future<void> updateQuiz(Quiz quiz) async {
    try {
      await _quizService.updateQuiz(quiz);
      
      // Reload quizzes
      await loadQuizzes();
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Delete a quiz
  Future<void> deleteQuiz(String quizId) async {
    try {
      await _quizService.deleteQuiz(quizId);
      
      // Reload quizzes
      await loadQuizzes();
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

/// State notifier for live quiz session
class LiveQuizSessionNotifier extends StateNotifier<LiveQuizSession?> {
  final QuizService _quizService;
  
  LiveQuizSessionNotifier(this._quizService) : super(null) {
    // Listen to session updates
    _quizService.sessionStream.listen((session) {
      state = session;
    });
  }
  
  /// Start a quiz session
  Future<void> startSession(Quiz quiz) async {
    try {
      await _quizService.startQuizSession(quiz);
    } catch (e) {
      throw Exception('Failed to start quiz session: $e');
    }
  }
  
  /// Start the next question
  Future<void> startNextQuestion() async {
    try {
      await _quizService.startNextQuestion();
    } catch (e) {
      throw Exception('Failed to start next question: $e');
    }
  }
  
  /// Skip the current question
  Future<void> skipCurrentQuestion() async {
    try {
      await _quizService.skipCurrentQuestion();
    } catch (e) {
      throw Exception('Failed to skip question: $e');
    }
  }
  
  /// Stop the quiz session
  Future<void> stopSession() async {
    try {
      await _quizService.stopQuizSession();
      state = null;
    } catch (e) {
      throw Exception('Failed to stop quiz session: $e');
    }
  }
}

/// State notifier for leaderboard
class LeaderboardNotifier extends StateNotifier<List<LeaderboardEntry>> {
  final QuizService _quizService;
  
  LeaderboardNotifier(this._quizService) : super([]) {
    _updateLeaderboard();
  }
  
  /// Update leaderboard from current session
  void _updateLeaderboard() {
    final leaderboard = _quizService.generateLeaderboard();
    state = leaderboard;
  }
  
  /// Refresh leaderboard
  void refresh() {
    _updateLeaderboard();
  }
}
