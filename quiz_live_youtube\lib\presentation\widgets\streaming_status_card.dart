import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/models/youtube_models.dart';

/// Card widget for displaying streaming status
class StreamingStatusCard extends StatelessWidget {
  final StreamStatus status;

  const StreamingStatusCard({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                _buildStatusIcon(theme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Streaming Status',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getStatusText(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _getStatusColor(theme),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(theme),
              ],
            ),
            
            // Streaming details (only show when streaming)
            if (status.state == StreamState.streaming || 
                status.state == StreamState.connected) ...[
              const SizedBox(height: 16),
              _buildStreamingDetails(theme),
            ],
            
            // Error message (only show when error)
            if (status.state == StreamState.error && status.error != null) ...[
              const SizedBox(height: 16),
              _buildErrorMessage(theme),
            ],
            
            // Last update time
            if (status.lastUpdate != null) ...[
              const SizedBox(height: 12),
              Text(
                'Last updated: ${_formatTime(status.lastUpdate!)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon(ThemeData theme) {
    IconData icon;
    Color color;
    
    switch (status.state) {
      case StreamState.streaming:
        icon = Icons.live_tv;
        color = Colors.red;
        break;
      case StreamState.connected:
        icon = Icons.wifi;
        color = Colors.green;
        break;
      case StreamState.connecting:
      case StreamState.reconnecting:
        icon = Icons.sync;
        color = Colors.orange;
        break;
      case StreamState.error:
        icon = Icons.error;
        color = Colors.red;
        break;
      case StreamState.disconnected:
      default:
        icon = Icons.wifi_off;
        color = Colors.grey;
        break;
    }
    
    Widget iconWidget = Icon(
      icon,
      color: color,
      size: 28,
    );
    
    // Add animation for connecting/reconnecting states
    if (status.state == StreamState.connecting || 
        status.state == StreamState.reconnecting) {
      iconWidget = iconWidget.animate(onPlay: (controller) => controller.repeat())
          .rotate(duration: 2000.ms);
    }
    
    // Add pulsing animation for streaming state
    if (status.state == StreamState.streaming) {
      iconWidget = iconWidget.animate(onPlay: (controller) => controller.repeat())
          .scale(begin: 1.0, end: 1.1, duration: 1000.ms)
          .then()
          .scale(begin: 1.1, end: 1.0, duration: 1000.ms);
    }
    
    return iconWidget;
  }

  Widget _buildStatusBadge(ThemeData theme) {
    Color backgroundColor;
    Color textColor;
    String text;
    
    switch (status.state) {
      case StreamState.streaming:
        backgroundColor = Colors.red;
        textColor = Colors.white;
        text = 'LIVE';
        break;
      case StreamState.connected:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        text = 'READY';
        break;
      case StreamState.connecting:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        text = 'CONNECTING';
        break;
      case StreamState.reconnecting:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        text = 'RECONNECTING';
        break;
      case StreamState.error:
        backgroundColor = Colors.red;
        textColor = Colors.white;
        text = 'ERROR';
        break;
      case StreamState.disconnected:
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        text = 'OFFLINE';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: theme.textTheme.labelSmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStreamingDetails(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'Bitrate',
                  '${(status.bitrate / 1000).toStringAsFixed(1)} Kbps',
                  Icons.speed,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDetailItem(
                  theme,
                  'Frame Rate',
                  '${status.frameRate.toStringAsFixed(1)} fps',
                  Icons.video_camera_back,
                ),
              ),
            ],
          ),
          if (status.droppedFrames > 0) ...[
            const SizedBox(height: 8),
            _buildDetailItem(
              theme,
              'Dropped Frames',
              '${status.droppedFrames}',
              Icons.warning,
              isWarning: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    ThemeData theme,
    String label,
    String value,
    IconData icon, {
    bool isWarning = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isWarning ? Colors.orange : theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isWarning ? Colors.orange : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.onErrorContainer,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              status.error!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText() {
    switch (status.state) {
      case StreamState.streaming:
        return 'Currently streaming live';
      case StreamState.connected:
        return 'Connected and ready to stream';
      case StreamState.connecting:
        return 'Connecting to stream server...';
      case StreamState.reconnecting:
        return 'Reconnecting to stream server...';
      case StreamState.error:
        return 'Stream error occurred';
      case StreamState.disconnected:
      default:
        return 'Not connected to stream server';
    }
  }

  Color _getStatusColor(ThemeData theme) {
    switch (status.state) {
      case StreamState.streaming:
        return Colors.red;
      case StreamState.connected:
        return Colors.green;
      case StreamState.connecting:
      case StreamState.reconnecting:
        return Colors.orange;
      case StreamState.error:
        return theme.colorScheme.error;
      case StreamState.disconnected:
      default:
        return theme.colorScheme.onSurface.withOpacity(0.6);
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
