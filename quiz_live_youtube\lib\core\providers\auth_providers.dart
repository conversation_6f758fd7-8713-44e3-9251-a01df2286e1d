import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/youtube_models.dart';
import 'service_providers.dart';

/// Provider for YouTube authentication status
final youtubeAuthStatusProvider = StateNotifierProvider<YouTubeAuthNotifier, AsyncValue<YouTubeToken?>>((ref) {
  final authService = ref.watch(youtubeAuthServiceProvider);
  return YouTubeAuthNotifier(authService);
});

/// Provider for checking if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authStatus = ref.watch(youtubeAuthStatusProvider);
  return authStatus.when(
    data: (token) => token != null,
    loading: () => false,
    error: (_, __) => false,
  );
});

/// Provider for current YouTube channel
final youtubeChannelProvider = FutureProvider<YouTubeChannel?>((ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  if (!isAuthenticated) return null;
  
  try {
    final apiService = ref.read(youtubeApiServiceProvider);
    return await apiService.getChannelInfo();
  } catch (e) {
    return null;
  }
});

/// State notifier for YouTube authentication
class YouTubeAuthNotifier extends StateNotifier<AsyncValue<YouTubeToken?>> {
  final YouTubeAuthService _authService;
  
  YouTubeAuthNotifier(this._authService) : super(const AsyncValue.loading()) {
    _loadInitialState();
  }
  
  /// Load initial authentication state
  Future<void> _loadInitialState() async {
    try {
      final token = _authService.currentToken;
      state = AsyncValue.data(token);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Sign in with YouTube
  Future<void> signIn() async {
    state = const AsyncValue.loading();
    
    try {
      final token = await _authService.signIn();
      state = AsyncValue.data(token);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Sign out from YouTube
  Future<void> signOut() async {
    try {
      await _authService.signOut();
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Refresh authentication token
  Future<void> refreshToken() async {
    try {
      await _authService.initialize(); // This will refresh if needed
      final token = _authService.currentToken;
      state = AsyncValue.data(token);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
