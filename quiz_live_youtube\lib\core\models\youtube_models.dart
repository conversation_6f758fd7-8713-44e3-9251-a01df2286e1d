import 'package:freezed_annotation/freezed_annotation.dart';

part 'youtube_models.freezed.dart';
part 'youtube_models.g.dart';

/// YouTube authentication token information
@freezed
class YouTubeToken with _$YouTubeToken {
  const factory YouTubeToken({
    required String accessToken,
    required String refreshToken,
    required String tokenType,
    required int expiresIn,
    required List<String> scopes,
    DateTime? expiresAt,
  }) = _YouTubeToken;

  factory YouTubeToken.fromJson(Map<String, dynamic> json) =>
      _$YouTubeTokenFromJson(json);
}

/// YouTube live broadcast information
@freezed
class YouTubeLiveBroadcast with _$YouTubeLiveBroadcast {
  const factory YouTubeLiveBroadcast({
    required String id,
    required String title,
    required String description,
    required String status,
    required String privacyStatus,
    String? streamKey,
    String? streamUrl,
    String? chatId,
    DateTime? scheduledStartTime,
    DateTime? actualStartTime,
    DateTime? actualEndTime,
  }) = _YouTubeLiveBroadcast;

  factory YouTubeLiveBroadcast.fromJson(Map<String, dynamic> json) =>
      _$YouTubeLiveBroadcastFromJson(json);
}

/// YouTube live stream information
@freezed
class YouTubeLiveStream with _$YouTubeLiveStream {
  const factory YouTubeLiveStream({
    required String id,
    required String title,
    required String status,
    required StreamSettings settings,
    String? ingestionAddress,
    String? streamName,
  }) = _YouTubeLiveStream;

  factory YouTubeLiveStream.fromJson(Map<String, dynamic> json) =>
      _$YouTubeLiveStreamFromJson(json);
}

/// Stream quality settings
@freezed
class StreamSettings with _$StreamSettings {
  const factory StreamSettings({
    @Default(1280) int width,
    @Default(720) int height,
    @Default(30) int frameRate,
    @Default(2500000) int bitrate,
    @Default('h264') String codec,
  }) = _StreamSettings;

  factory StreamSettings.fromJson(Map<String, dynamic> json) =>
      _$StreamSettingsFromJson(json);
}

/// YouTube chat message
@freezed
class YouTubeChatMessage with _$YouTubeChatMessage {
  const factory YouTubeChatMessage({
    required String id,
    required String authorChannelId,
    required String authorDisplayName,
    required String messageText,
    required DateTime publishedAt,
    @Default(false) bool isFromOwner,
    @Default(false) bool isModerator,
    @Default(false) bool isSponsor,
  }) = _YouTubeChatMessage;

  factory YouTubeChatMessage.fromJson(Map<String, dynamic> json) =>
      _$YouTubeChatMessageFromJson(json);
}

/// YouTube channel information
@freezed
class YouTubeChannel with _$YouTubeChannel {
  const factory YouTubeChannel({
    required String id,
    required String title,
    required String description,
    String? thumbnailUrl,
    int? subscriberCount,
    int? videoCount,
  }) = _YouTubeChannel;

  factory YouTubeChannel.fromJson(Map<String, dynamic> json) =>
      _$YouTubeChannelFromJson(json);
}

/// Stream status information
@freezed
class StreamStatus with _$StreamStatus {
  const factory StreamStatus({
    @Default(StreamState.disconnected) StreamState state,
    @Default(0) int bitrate,
    @Default(0) double frameRate,
    @Default(0) int droppedFrames,
    String? error,
    DateTime? lastUpdate,
  }) = _StreamStatus;

  factory StreamStatus.fromJson(Map<String, dynamic> json) =>
      _$StreamStatusFromJson(json);
}

/// Stream connection states
enum StreamState {
  disconnected,
  connecting,
  connected,
  streaming,
  error,
  reconnecting,
}
